
import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import { SupabaseBlogProvider } from './context/SupabaseBlogContext';
import { performanceMonitor, trackPageView } from './utils/performanceMonitor';

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);

// Initialize performance monitoring
trackPageView('app-init');

// Conditionally disable StrictMode in development to prevent editor double-render issues
const isDevelopment = import.meta.env.DEV;
const AppWrapper = () => (
  <BrowserRouter>
    <SupabaseBlogProvider>
      <App />
    </SupabaseBlogProvider>
  </BrowserRouter>
);

root.render(
  isDevelopment ? (
    <AppWrapper />
  ) : (
    <React.StrictMode>
      <AppWrapper />
    </React.StrictMode>
  )
);

// Track app initialization complete
performance.mark('app-init-complete');
if (import.meta.env.DEV) {
  // Log performance metrics in development
  setTimeout(() => {
    const metrics = performanceMonitor.getMetrics();
    console.table(metrics);
  }, 5000);
}
