
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>behindyourbrain - Creative Magazine</title>

    <!-- Resource hints for performance -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com">
    <link rel="dns-prefetch" href="https://fonts.gstatic.com">
    <link rel="dns-prefetch" href="https://esm.sh">

    <!-- Preconnect to critical origins -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://esm.sh" crossorigin>

    <!-- Preload critical fonts for faster rendering -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700;800&display=swap" as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Fallback for browsers that don't support preload -->
    <noscript>
      <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
      <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@700;800&display=swap" rel="stylesheet">
    </noscript>

    <!-- Inline critical font fallbacks to prevent layout shift -->
    <style>
      /* Font fallback system to prevent layout shifts */
      @font-face {
        font-family: 'Inter-fallback';
        src: local('Arial'), local('Helvetica'), local('sans-serif');
        font-display: swap;
        ascent-override: 90%;
        descent-override: 22%;
        line-gap-override: 0%;
        size-adjust: 107%;
      }

      @font-face {
        font-family: 'Playfair-fallback';
        src: local('Times New Roman'), local('Times'), local('serif');
        font-display: swap;
        ascent-override: 105%;
        descent-override: 35%;
        line-gap-override: 0%;
        size-adjust: 95%;
      }

      /* Apply fallbacks before web fonts load */
      body {
        font-family: 'Inter-fallback', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .font-serif {
        font-family: 'Playfair-fallback', Georgia, 'Times New Roman', serif;
      }

      /* Override with web fonts when loaded */
      .fonts-loaded body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      .fonts-loaded .font-serif {
        font-family: 'Playfair Display', Georgia, 'Times New Roman', serif;
      }

      /* Critical CSS - Above the fold styles */
      .min-h-screen { min-height: 100vh; }
      .flex { display: flex; }
      .flex-col { flex-direction: column; }
      .items-center { align-items: center; }
      .justify-center { justify-content: center; }
      .text-center { text-align: center; }
      .animate-spin { animation: spin 1s linear infinite; }
      .rounded-full { border-radius: 9999px; }
      .border-b-2 { border-bottom-width: 2px; }
      .mx-auto { margin-left: auto; margin-right: auto; }
      .mb-4 { margin-bottom: 1rem; }
      .h-12 { height: 3rem; }
      .w-12 { width: 3rem; }

      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }

      /* Performance optimizations */
      * {
        box-sizing: border-box;
      }

      html {
        -webkit-text-size-adjust: 100%;
        text-size-adjust: 100%;
      }

      body {
        margin: 0;
        line-height: 1.5;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      img {
        max-width: 100%;
        height: auto;
      }
    </style>

    <!-- Preload critical CSS -->
    <link rel="preload" href="/index.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/index.css"></noscript>
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.7.1",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.11.0",
    "recharts": "https://esm.sh/recharts@^3.1.0"
  }
}
</script>
</head>
  <body class="bg-light dark:bg-dark text-dark-text dark:text-light">
    <div id="root"></div>

    <!-- Font loading detection script -->
    <script>
      // Detect when fonts are loaded and add class to prevent layout shift
      if ('fonts' in document) {
        Promise.all([
          document.fonts.load('400 16px Inter'),
          document.fonts.load('700 24px Playfair Display')
        ]).then(() => {
          document.documentElement.classList.add('fonts-loaded');
        }).catch(() => {
          // Fallback: add class after timeout
          setTimeout(() => {
            document.documentElement.classList.add('fonts-loaded');
          }, 3000);
        });
      } else {
        // Fallback for browsers without Font Loading API
        setTimeout(() => {
          document.documentElement.classList.add('fonts-loaded');
        }, 3000);
      }
    </script>

  <script type="module" src="/index.tsx"></script>
</body>
</html>