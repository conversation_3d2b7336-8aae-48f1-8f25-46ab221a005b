import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    const isProduction = mode === 'production';

    return {
      plugins: [
        tailwindcss(),
      ],
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      build: {
        // Optimize bundle splitting for better caching
        rollupOptions: {
          output: {
            manualChunks: {
              // Vendor chunk for stable dependencies
              vendor: ['react', 'react-dom', 'react-router-dom'],
              // Supabase chunk
              supabase: ['@supabase/supabase-js'],
              // UI components chunk
              ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-select'],
              // Editor chunk (heavy dependency)
              editor: ['@tinymce/tinymce-react', 'tinymce'],
              // Utils chunk
              utils: ['clsx', 'tailwind-merge', 'class-variance-authority'],
            },
          },
        },
        // Enable tree shaking
        minify: isProduction ? 'terser' : false,
        terserOptions: isProduction ? {
          compress: {
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info', 'console.debug'],
          },
        } : undefined,
        // Optimize chunk size
        chunkSizeWarningLimit: 1000,
        // Enable source maps for debugging in production
        sourcemap: isProduction ? 'hidden' : true,
      },
      server: {
        historyApiFallback: true,
      },
      preview: {
        historyApiFallback: true,
      }
    };
});
