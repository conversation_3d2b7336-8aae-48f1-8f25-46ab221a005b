// Performance monitoring utility for Core Web Vitals and custom metrics
interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  url: string;
}

interface WebVitalsMetric extends PerformanceMetric {
  id: string;
  delta: number;
  rating: 'good' | 'needs-improvement' | 'poor';
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = true;

  constructor() {
    this.initializeObservers();
    this.trackPageLoad();
  }

  private initializeObservers(): void {
    if (!this.isEnabled || typeof window === 'undefined') return;

    // Track Largest Contentful Paint (LCP)
    this.observeMetric('largest-contentful-paint', (entries) => {
      const lastEntry = entries[entries.length - 1];
      this.recordMetric('LCP', lastEntry.startTime, this.getRating('LCP', lastEntry.startTime));
    });

    // Track First Input Delay (FID)
    this.observeMetric('first-input', (entries) => {
      const firstEntry = entries[0];
      const fid = firstEntry.processingStart - firstEntry.startTime;
      this.recordMetric('FID', fid, this.getRating('FID', fid));
    });

    // Track Cumulative Layout Shift (CLS)
    this.observeMetric('layout-shift', (entries) => {
      let clsValue = 0;
      for (const entry of entries) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
        }
      }
      this.recordMetric('CLS', clsValue, this.getRating('CLS', clsValue));
    });

    // Track Time to First Byte (TTFB)
    this.observeMetric('navigation', (entries) => {
      const navigationEntry = entries[0] as PerformanceNavigationTiming;
      const ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
      this.recordMetric('TTFB', ttfb, this.getRating('TTFB', ttfb));
    });
  }

  private observeMetric(type: string, callback: (entries: PerformanceEntry[]) => void): void {
    try {
      const observer = new PerformanceObserver((list) => {
        callback(list.getEntries());
      });
      observer.observe({ type, buffered: true });
      this.observers.push(observer);
    } catch (error) {
      console.warn(`Failed to observe ${type}:`, error);
    }
  }

  private getRating(metric: string, value: number): 'good' | 'needs-improvement' | 'poor' {
    const thresholds = {
      LCP: { good: 2500, poor: 4000 },
      FID: { good: 100, poor: 300 },
      CLS: { good: 0.1, poor: 0.25 },
      TTFB: { good: 800, poor: 1800 },
    };

    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return 'good';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  }

  private recordMetric(name: string, value: number, rating?: string): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url: window.location.href,
    };

    this.metrics.push(metric);

    // Log to console in development
    if (import.meta.env.DEV) {
      console.log(`📊 ${name}: ${value.toFixed(2)}ms${rating ? ` (${rating})` : ''}`);
    }

    // Send to analytics in production (implement your analytics service)
    if (import.meta.env.PROD) {
      this.sendToAnalytics(metric);
    }
  }

  private trackPageLoad(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      // Track page load time
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.recordMetric('Page Load', navigation.loadEventEnd - navigation.fetchStart);
        this.recordMetric('DOM Content Loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart);
        this.recordMetric('First Paint', this.getFirstPaint());
        this.recordMetric('First Contentful Paint', this.getFirstContentfulPaint());
      }
    });
  }

  private getFirstPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
    return firstPaint ? firstPaint.startTime : 0;
  }

  private getFirstContentfulPaint(): number {
    const paintEntries = performance.getEntriesByType('paint');
    const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint');
    return firstContentfulPaint ? firstContentfulPaint.startTime : 0;
  }

  private sendToAnalytics(metric: PerformanceMetric): void {
    // Implement your analytics service here
    // Example: Google Analytics, DataDog, New Relic, etc.
    
    // For now, we'll use a simple beacon API
    if ('sendBeacon' in navigator) {
      const data = JSON.stringify(metric);
      navigator.sendBeacon('/api/analytics/performance', data);
    }
  }

  // Public methods
  public trackCustomMetric(name: string, value: number): void {
    this.recordMetric(name, value);
  }

  public trackUserTiming(name: string, startMark?: string, endMark?: string): void {
    try {
      if (startMark && endMark) {
        performance.measure(name, startMark, endMark);
      }
      const measures = performance.getEntriesByName(name, 'measure');
      if (measures.length > 0) {
        const measure = measures[measures.length - 1];
        this.recordMetric(name, measure.duration);
      }
    } catch (error) {
      console.warn(`Failed to track user timing ${name}:`, error);
    }
  }

  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  public clearMetrics(): void {
    this.metrics = [];
  }

  public disable(): void {
    this.isEnabled = false;
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for manual tracking
export const trackPageView = (pageName: string): void => {
  performance.mark(`page-${pageName}-start`);
  performanceMonitor.trackCustomMetric('Page View', Date.now());
};

export const trackInteraction = (action: string, startTime: number): void => {
  const duration = Date.now() - startTime;
  performanceMonitor.trackCustomMetric(`Interaction: ${action}`, duration);
};

export const trackAPICall = (endpoint: string, duration: number, success: boolean): void => {
  performanceMonitor.trackCustomMetric(`API: ${endpoint}`, duration);
  performanceMonitor.trackCustomMetric(`API Success: ${endpoint}`, success ? 1 : 0);
};

// React hook for component performance tracking
export const usePerformanceTracking = (componentName: string) => {
  const startTime = performance.now();
  
  return {
    trackRender: () => {
      const renderTime = performance.now() - startTime;
      performanceMonitor.trackCustomMetric(`Component Render: ${componentName}`, renderTime);
    },
    trackInteraction: (action: string) => {
      const interactionStart = performance.now();
      return () => {
        const duration = performance.now() - interactionStart;
        performanceMonitor.trackCustomMetric(`${componentName}: ${action}`, duration);
      };
    }
  };
};
