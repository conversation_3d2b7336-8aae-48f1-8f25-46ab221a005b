
import React, { useContext } from 'react';
import { BlogContext } from '../../context/SupabaseBlogContext';
import PostCard from '../../components/PostCard';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
    const context = useContext(BlogContext);

    if (!context) {
        return <div className="flex items-center justify-center min-h-96">
            <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading...</p>
            </div>
        </div>;
    }

    const { posts } = context;
    const publishedPosts = posts.filter(post => post.status === 'published');
    const articles = publishedPosts.slice(0, 12);

    return (
        <>
            <title>My Awesome Blog</title>
            <meta name="description" content="Welcome to my awesome blog where I write about cool stuff." />
            
            {/* Main Article Grid */}
            <main className="py-8 sm:py-12">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-12 gap-6 sm:gap-8">
                    {/* First two large articles */}
                    {articles.slice(0, 2).map(article => (
                        <div key={article.id} className="sm:col-span-2 lg:col-span-6">
                            <PostCard post={article} variant="wireframe" isLarge={true} />
                        </div>
                    ))}
                    
                    {/* Remaining smaller articles */}
                    {articles.slice(2, 12).map(article => (
                        <div key={article.id} className="sm:col-span-1 lg:col-span-4">
                            <PostCard post={article} variant="wireframe" />
                        </div>
                    ))}
                </div>
                
                {/* View All Button */}
                <div className="text-center mt-8 sm:mt-12">
                    {publishedPosts.length > 12 && (
                        <Link 
                            to="/all-posts" 
                            className="inline-block border border-slate-400 dark:border-slate-600 text-slate-600 dark:text-slate-300 font-medium py-3 px-6 sm:px-8 text-sm hover:bg-slate-100 dark:hover:bg-medium-dark transition-colors"
                        >
                            View all trending articles
                        </Link>
                    )}
                </div>
            </main>

            {/* Instagram Feed Section - Optimized for layout stability */}
            <section className="py-12 sm:py-16" style={{ contain: 'layout style paint' }}>
                <div className="text-center mb-6 sm:mb-8">
                    <div className="inline-flex items-center gap-3 text-slate-800 dark:text-slate-200 text-base sm:text-lg">
                        <div className="w-5 h-5 sm:w-6 sm:h-6 border-2 border-slate-400 dark:border-slate-600 rounded-md flex items-center justify-center flex-shrink-0">
                            <div className="w-2 h-2 sm:w-3 sm:h-3 border-2 border-slate-400 dark:border-slate-600 rounded-full"></div>
                        </div>
                        <span className="font-medium font-sans">@behindyourbrain_magazine</span>
                    </div>
                    <h2 className="text-2xl sm:text-3xl font-serif text-slate-800 dark:text-slate-200 mt-2 leading-tight">Follow Me On Instagram</h2>
                    <p className="text-muted-foreground mt-2 text-sm sm:text-base px-4 leading-relaxed">Stay updated with our latest stories and behind-the-scenes content</p>
                </div>
                {/* Fixed height container to prevent layout shift */}
                <div className="min-h-[200px] sm:min-h-[150px] md:min-h-[120px]">
                    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-6 gap-2 sm:gap-3">
                        {[...Array(6)].map((_, i) => (
                            <div
                                key={i}
                                className="aspect-square bg-muted border border-card hover:opacity-80 transition-opacity cursor-pointer rounded-sm overflow-hidden relative"
                                style={{
                                    contain: 'layout style paint',
                                    minHeight: '80px',
                                    minWidth: '80px'
                                }}
                            >
                                {/* Skeleton loading animation */}
                                <div className="absolute inset-0 bg-gradient-to-r from-muted via-muted/50 to-muted animate-pulse"></div>
                                {/* Placeholder content */}
                                <div className="absolute inset-0 flex items-center justify-center text-muted-foreground text-xs opacity-50">
                                    <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                                    </svg>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </section>
        </>
    );
};

export default HomePage;
